"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.documentFunctions = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
/**
 * Approve a document (simplified without status management)
 */
const approveDocument = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        const { documentId } = data;
        if (!documentId) {
            throw new functions.https.HttpsError("invalid-argument", "Document ID is required");
        }
        // Check user permissions
        const userDoc = await admin
            .firestore()
            .collection("users")
            .doc(context.auth.uid)
            .get();
        const user = userDoc.data();
        if (!user || user.role !== "admin") {
            throw new functions.https.HttpsError("permission-denied", "Only admins can approve documents");
        }
        // Update document
        await admin
            .firestore()
            .collection("document-metadata")
            .doc(documentId)
            .update({
            approvedBy: context.auth.uid,
            approvedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        // Log activity
        await admin
            .firestore()
            .collection("activities")
            .add({
            type: "document_approved",
            documentId: documentId,
            userId: context.auth.uid,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            details: "Document approved by admin",
        });
        return {
            success: true,
            message: "Document approved successfully",
        };
    }
    catch (error) {
        console.error("Error approving document:", error);
        throw new functions.https.HttpsError("internal", `Failed to approve document: ${error}`);
    }
});
/**
 * Reject a document (simplified without status management)
 */
const rejectDocument = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        const { documentId, reason } = data;
        if (!documentId) {
            throw new functions.https.HttpsError("invalid-argument", "Document ID is required");
        }
        // Check user permissions
        const userDoc = await admin
            .firestore()
            .collection("users")
            .doc(context.auth.uid)
            .get();
        const user = userDoc.data();
        if (!user || user.role !== "admin") {
            throw new functions.https.HttpsError("permission-denied", "Only admins can reject documents");
        }
        // Update document
        await admin
            .firestore()
            .collection("document-metadata")
            .doc(documentId)
            .update({
            rejectedBy: context.auth.uid,
            rejectedAt: admin.firestore.FieldValue.serverTimestamp(),
            rejectionReason: reason || "No reason provided",
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        // Log activity
        await admin
            .firestore()
            .collection("activities")
            .add({
            type: "document_rejected",
            documentId: documentId,
            userId: context.auth.uid,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            details: `Document rejected by admin: ${reason || "No reason provided"}`,
        });
        return {
            success: true,
            message: "Document rejected successfully",
        };
    }
    catch (error) {
        console.error("Error rejecting document:", error);
        throw new functions.https.HttpsError("internal", `Failed to reject document: ${error}`);
    }
});
/**
 * Bulk document operations
 */
const bulkDocumentOperations = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        const { documentIds, operation, reason } = data;
        if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
            throw new functions.https.HttpsError("invalid-argument", "Document IDs array is required");
        }
        if (!operation || !["approve", "reject", "delete"].includes(operation)) {
            throw new functions.https.HttpsError("invalid-argument", "Valid operation (approve, reject, delete) is required");
        }
        // Check user permissions
        const userDoc = await admin
            .firestore()
            .collection("users")
            .doc(context.auth.uid)
            .get();
        const user = userDoc.data();
        if (!user || user.role !== "admin") {
            throw new functions.https.HttpsError("permission-denied", "Only admins can perform bulk document operations");
        }
        const batch = admin.firestore().batch();
        const results = [];
        for (const documentId of documentIds) {
            try {
                const docRef = admin.firestore().collection("document-metadata").doc(documentId);
                switch (operation) {
                    case "approve":
                        batch.update(docRef, {
                            approvedBy: context.auth.uid,
                            approvedAt: admin.firestore.FieldValue.serverTimestamp(),
                            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                        });
                        break;
                    case "reject":
                        batch.update(docRef, {
                            rejectedBy: context.auth.uid,
                            rejectedAt: admin.firestore.FieldValue.serverTimestamp(),
                            rejectionReason: reason || "Bulk rejection",
                            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                        });
                        break;
                    case "delete":
                        batch.update(docRef, {
                            isActive: false,
                            deletedBy: context.auth.uid,
                            deletedAt: admin.firestore.FieldValue.serverTimestamp(),
                            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
                        });
                        break;
                }
                results.push({ documentId, success: true });
            }
            catch (error) {
                results.push({ documentId, success: false, error: String(error) });
            }
        }
        await batch.commit();
        // Log activity
        await admin
            .firestore()
            .collection("activities")
            .add({
            type: `bulk_document_${operation}`,
            userId: context.auth.uid,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            details: `Bulk ${operation} operation performed on ${documentIds.length} documents`,
            documentIds: documentIds,
        });
        return {
            success: true,
            message: `Bulk ${operation} operation completed`,
            results: results,
        };
    }
    catch (error) {
        console.error("Error in bulk document operations:", error);
        throw new functions.https.HttpsError("internal", `Bulk document operation failed: ${error}`);
    }
});
/**
 * Enhanced path resolution helper functions for comprehensive file deletion
 */
/**
 * Validate and resolve the correct storage path for a file
 */
async function validateAndResolveStoragePath(bucket, originalPath, fileName, documentData, documentId) {
    console.log(`🔍 Validating storage path: ${originalPath}`);
    // Generate all possible storage paths based on different upload patterns
    const possiblePaths = generateAllPossibleStoragePaths(originalPath, fileName, documentData, documentId);
    console.log(`📋 Generated ${possiblePaths.length} possible paths to check`);
    // Check each path for file existence
    for (const path of possiblePaths) {
        try {
            const file = bucket.file(path);
            const [exists] = await file.exists();
            if (exists) {
                console.log(`✅ Found file at validated path: ${path}`);
                return path;
            }
            else {
                console.log(`❌ File not found at: ${path}`);
            }
        }
        catch (error) {
            console.log(`⚠️ Error checking path ${path}: ${error.message}`);
        }
    }
    console.log(`❌ File not found in any of the ${possiblePaths.length} possible locations`);
    return null;
}
/**
 * Generate all possible storage paths based on different upload patterns
 */
function generateAllPossibleStoragePaths(originalPath, fileName, documentData, documentId) {
    const paths = [];
    const sanitizedFileName = sanitizeFileName(fileName);
    const fileExtension = getFileExtension(fileName);
    // 1. Original stored path (highest priority)
    if (originalPath && originalPath.trim()) {
        paths.push(originalPath.trim());
    }
    // 2. Direct documents folder patterns
    paths.push(`documents/${fileName}`);
    paths.push(`documents/${sanitizedFileName}`);
    // 3. User-specific folder patterns
    if (documentData === null || documentData === void 0 ? void 0 : documentData.uploadedBy) {
        paths.push(`documents/${documentData.uploadedBy}/${fileName}`);
        paths.push(`documents/${documentData.uploadedBy}/${sanitizedFileName}`);
    }
    // 4. Category-based folder patterns
    if ((documentData === null || documentData === void 0 ? void 0 : documentData.category) && documentData.category !== 'uncategorized') {
        paths.push(`documents/categories/${documentData.category}/${fileName}`);
        paths.push(`documents/categories/${documentData.category}/${sanitizedFileName}`);
        // User + category patterns
        if (documentData === null || documentData === void 0 ? void 0 : documentData.uploadedBy) {
            paths.push(`documents/categories/${documentData.category}/${documentData.uploadedBy}/${fileName}`);
            paths.push(`documents/categories/${documentData.category}/${documentData.uploadedBy}/${sanitizedFileName}`);
        }
    }
    // 5. Document ID-based patterns (for files named with document ID)
    if (fileExtension) {
        paths.push(`documents/${documentId}.${fileExtension}`);
        if (documentData === null || documentData === void 0 ? void 0 : documentData.uploadedBy) {
            paths.push(`documents/${documentData.uploadedBy}/${documentId}.${fileExtension}`);
        }
        if ((documentData === null || documentData === void 0 ? void 0 : documentData.category) && documentData.category !== 'uncategorized') {
            paths.push(`documents/categories/${documentData.category}/${documentId}.${fileExtension}`);
        }
    }
    // 6. Legacy patterns (for backward compatibility)
    paths.push(`documents/${documentId}`);
    // Remove duplicates while preserving order
    return [...new Set(paths)];
}
/**
 * Attempt comprehensive deletion with all possible path patterns
 */
async function attemptComprehensiveDeletion(bucket, fileName, documentData, documentId) {
    console.log(`🔄 Attempting comprehensive deletion for: ${fileName}`);
    const allPaths = generateAllPossibleStoragePaths('', // No original path for comprehensive search
    fileName, documentData, documentId);
    console.log(`📋 Attempting deletion across ${allPaths.length} possible paths`);
    for (const path of allPaths) {
        try {
            const file = bucket.file(path);
            const [exists] = await file.exists();
            if (exists) {
                await file.delete();
                console.log(`✅ Successfully deleted from comprehensive search path: ${path}`);
                return true;
            }
        }
        catch (error) {
            console.log(`⚠️ Comprehensive deletion attempt failed for ${path}: ${error.message}`);
        }
    }
    console.log(`❌ Comprehensive deletion failed - file not found in any location`);
    return false;
}
/**
 * Sanitize filename for storage path matching
 */
function sanitizeFileName(fileName) {
    return fileName
        .replace(/[^\w\s\-\.]/g, '_')
        .replace(/\s+/g, '_')
        .toLowerCase();
}
/**
 * Get file extension from filename
 */
function getFileExtension(fileName) {
    const parts = fileName.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
}
/**
 * Delete document permanently (from both Firestore and Storage)
 * This function provides atomic deletion with proper error handling
 */
const deleteDocument = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        const { documentId } = data;
        if (!documentId) {
            throw new functions.https.HttpsError("invalid-argument", "Document ID is required");
        }
        console.log(`🗑️ Starting delete operation for document: ${documentId}`);
        // ADMIN-ONLY: Check user permissions
        const userDoc = await admin
            .firestore()
            .collection("users")
            .doc(context.auth.uid)
            .get();
        const user = userDoc.data();
        if (!user || user.role !== "admin") {
            throw new functions.https.HttpsError("permission-denied", "Access denied: Only administrators can delete files");
        }
        console.log(`✅ Admin permission verified for user: ${context.auth.uid}`);
        // Get document metadata from Firestore
        const docRef = admin.firestore().collection("document-metadata").doc(documentId);
        const docSnapshot = await docRef.get();
        if (!docSnapshot.exists) {
            console.log(`⚠️ Document not found in Firestore: ${documentId}`);
            throw new functions.https.HttpsError("not-found", "Document not found in database");
        }
        const documentData = docSnapshot.data();
        const fileName = (documentData === null || documentData === void 0 ? void 0 : documentData.fileName) || "Unknown File";
        const filePath = (documentData === null || documentData === void 0 ? void 0 : documentData.filePath) || "";
        console.log(`📁 Found document: ${fileName} at path: ${filePath}`);
        // ENHANCED ATOMIC OPERATION: Delete from both Storage and Firestore with comprehensive path resolution
        const bucket = admin.storage().bucket();
        let storageDeleted = false;
        let firestoreDeleted = false;
        let actualStoragePath = null;
        try {
            // Step 1: Enhanced Storage Deletion with comprehensive path resolution
            if (filePath) {
                console.log(`🔍 Starting enhanced path resolution for: ${fileName}`);
                // First, try to validate and resolve the correct storage path
                actualStoragePath = await validateAndResolveStoragePath(bucket, filePath, fileName, documentData, documentId);
                if (actualStoragePath) {
                    try {
                        const file = bucket.file(actualStoragePath);
                        await file.delete();
                        storageDeleted = true;
                        console.log(`✅ Successfully deleted from resolved Storage path: ${actualStoragePath}`);
                    }
                    catch (deleteError) {
                        console.log(`❌ Failed to delete from resolved path ${actualStoragePath}: ${deleteError.message}`);
                        // If resolved path fails, try comprehensive fallback
                        storageDeleted = await attemptComprehensiveDeletion(bucket, fileName, documentData, documentId);
                    }
                }
                else {
                    console.log(`⚠️ Could not resolve storage path, attempting comprehensive fallback`);
                    // Attempt comprehensive deletion with all possible paths
                    storageDeleted = await attemptComprehensiveDeletion(bucket, fileName, documentData, documentId);
                }
                if (!storageDeleted) {
                    console.log(`❌ All storage deletion attempts failed for: ${fileName}`);
                    console.log(`📊 Attempted paths logged above for debugging`);
                }
            }
            else {
                console.log(`⚠️ No file path found in document metadata, attempting path reconstruction`);
                // Try to reconstruct path from available data
                storageDeleted = await attemptComprehensiveDeletion(bucket, fileName, documentData, documentId);
            }
            // Step 2: Delete from Firestore
            await docRef.delete();
            firestoreDeleted = true;
            console.log(`✅ Successfully deleted from Firestore: ${documentId}`);
            // Step 3: Enhanced activity logging with path resolution details
            await admin
                .firestore()
                .collection("activities")
                .add({
                type: "document_deleted",
                documentId: documentId,
                userId: context.auth.uid,
                timestamp: admin.firestore.FieldValue.serverTimestamp(),
                details: `Document permanently deleted: ${fileName}`,
                metadata: {
                    fileName: fileName,
                    originalFilePath: filePath,
                    actualStoragePath: actualStoragePath,
                    storageDeleted: storageDeleted,
                    firestoreDeleted: firestoreDeleted,
                    enhancedPathResolution: true,
                    deletionMethod: actualStoragePath ? 'resolved_path' : 'comprehensive_search',
                },
            });
            console.log(`✅ Delete operation completed successfully for: ${fileName}`);
            return {
                success: true,
                message: `Document "${fileName}" deleted successfully`,
                details: {
                    documentId: documentId,
                    fileName: fileName,
                    storageDeleted: storageDeleted,
                    firestoreDeleted: firestoreDeleted,
                },
            };
        }
        catch (operationError) {
            console.error(`❌ Enhanced delete operation failed: ${operationError.message}`);
            console.error(`📊 Deletion context - File: ${fileName}, Original Path: ${filePath}, Resolved Path: ${actualStoragePath}`);
            // Enhanced error logging with diagnostic information
            await admin
                .firestore()
                .collection("activities")
                .add({
                type: "document_delete_failed",
                documentId: documentId,
                userId: context.auth.uid,
                timestamp: admin.firestore.FieldValue.serverTimestamp(),
                details: `Failed to delete document: ${fileName}`,
                error: operationError.message,
                diagnostics: {
                    originalFilePath: filePath,
                    resolvedStoragePath: actualStoragePath,
                    storageDeleted: storageDeleted,
                    firestoreDeleted: firestoreDeleted,
                    enhancedPathResolution: true,
                    errorType: operationError.constructor.name,
                },
            });
            // Provide more specific error messages based on deletion status
            let errorMessage = `Failed to delete document: ${operationError.message}`;
            if (storageDeleted && !firestoreDeleted) {
                errorMessage = `Storage deleted but Firestore deletion failed: ${operationError.message}`;
            }
            else if (!storageDeleted && firestoreDeleted) {
                errorMessage = `Firestore deleted but storage deletion failed: ${operationError.message}`;
            }
            throw new functions.https.HttpsError("internal", errorMessage);
        }
    }
    catch (error) {
        console.error("Error in deleteDocument function:", error);
        // Re-throw HttpsError as-is
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        // Wrap other errors
        throw new functions.https.HttpsError("internal", `Delete operation failed: ${error.message || error}`);
    }
});
/**
 * Generate document report
 */
const generateDocumentReport = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        // Check user permissions
        const userDoc = await admin
            .firestore()
            .collection("users")
            .doc(context.auth.uid)
            .get();
        const user = userDoc.data();
        if (!user || user.role !== "admin") {
            throw new functions.https.HttpsError("permission-denied", "Only admins can generate document reports");
        }
        const { startDate, endDate, categoryId } = data;
        let query = admin
            .firestore()
            .collection("document-metadata")
            .where("isActive", "==", true);
        if (startDate) {
            query = query.where("uploadedAt", ">=", new Date(startDate));
        }
        if (endDate) {
            query = query.where("uploadedAt", "<=", new Date(endDate));
        }
        if (categoryId) {
            query = query.where("category", "==", categoryId);
        }
        const documentsSnapshot = await query.get();
        const documents = documentsSnapshot.docs.map(doc => (Object.assign({ id: doc.id }, doc.data())));
        // Generate statistics
        const stats = {
            totalDocuments: documents.length,
            documentsByCategory: {},
            documentsByType: {},
            documentsByUploader: {},
            totalSize: 0,
        };
        documents.forEach((doc) => {
            // Count by category
            const category = doc.category || "Uncategorized";
            stats.documentsByCategory[category] = (stats.documentsByCategory[category] || 0) + 1;
            // Count by type
            const type = getFileTypeFromName(doc.fileName || "");
            stats.documentsByType[type] = (stats.documentsByType[type] || 0) + 1;
            // Count by uploader
            const uploader = doc.uploadedBy || "Unknown";
            stats.documentsByUploader[uploader] = (stats.documentsByUploader[uploader] || 0) + 1;
            // Sum file sizes
            stats.totalSize += doc.fileSize || 0;
        });
        // Log activity
        await admin
            .firestore()
            .collection("activities")
            .add({
            type: "document_report_generated",
            userId: context.auth.uid,
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            details: `Document report generated for ${documents.length} documents`,
        });
        return {
            success: true,
            report: {
                generatedAt: new Date().toISOString(),
                generatedBy: context.auth.uid,
                filters: { startDate, endDate, categoryId },
                statistics: stats,
                documents: documents,
            },
        };
    }
    catch (error) {
        console.error("Error generating document report:", error);
        throw new functions.https.HttpsError("internal", `Failed to generate document report: ${error}`);
    }
});
// Helper function
function getFileTypeFromName(fileName) {
    var _a;
    const extension = (_a = fileName.split(".").pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();
    switch (extension) {
        case "pdf":
            return "PDF";
        case "doc":
        case "docx":
            return "DOC";
        case "xls":
        case "xlsx":
            return "Excel";
        case "ppt":
        case "pptx":
            return "PPT";
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
            return "Image";
        case "txt":
            return "Text";
        default:
            return "Other";
    }
}
exports.documentFunctions = {
    approveDocument,
    rejectDocument,
    deleteDocument,
    bulkDocumentOperations,
    generateDocumentReport,
};
//# sourceMappingURL=documentManagement.js.map