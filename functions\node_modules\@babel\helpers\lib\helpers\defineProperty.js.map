{"version": 3, "names": ["_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "_defineProperty", "obj", "key", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "enumerable", "configurable", "writable"], "sources": ["../../src/helpers/defineProperty.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\nimport toPropertyKey from \"./toPropertyKey.ts\";\n\nexport default function _defineProperty<T extends object>(\n  obj: T,\n  key: PropertyKey,\n  value: any,\n) {\n  key = toPropertyKey(key);\n  // Shortcircuit the slow defineProperty path when possible.\n  // We are trying to avoid issues where setters defined on the\n  // prototype cause side effects under the fast path of simple\n  // assignment. By checking for existence of the property with\n  // the in operator, we can optimize most of this overhead away.\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true,\n    });\n  } else {\n    // @ts-expect-error - Explicitly assigning to generic type key\n    obj[key] = value;\n  }\n  return obj;\n}\n"], "mappings": ";;;;;;AACA,IAAAA,cAAA,GAAAC,OAAA;AAEe,SAASC,eAAeA,CACrCC,GAAM,EACNC,GAAgB,EAChBC,KAAU,EACV;EACAD,GAAG,GAAG,IAAAE,sBAAa,EAACF,GAAG,CAAC;EAMxB,IAAIA,GAAG,IAAID,GAAG,EAAE;IACdI,MAAM,CAACC,cAAc,CAACL,GAAG,EAAEC,GAAG,EAAE;MAC9BC,KAAK,EAAEA,KAAK;MACZI,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IAELR,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAClB;EACA,OAAOF,GAAG;AACZ", "ignoreList": []}