<?xml version="1.0" encoding="utf-8"?>
<!-- Debug-specific network security configuration for Firebase App Check -->
<network-security-config>
    <!-- Firebase App Check and Core Services Configuration (Debug Mode) -->
    <domain-config cleartextTrafficPermitted="false">
        <!-- Firebase App Check specific domains -->
        <domain includeSubdomains="true">firebaseappcheck.googleapis.com</domain>
        <domain includeSubdomains="true">firebaseappcheck-pa.googleapis.com</domain>
        <domain includeSubdomains="true">firebaseappcheck-pa.clients6.google.com</domain>
        
        <!-- Firebase Core Services -->
        <domain includeSubdomains="true">firebasestorage.googleapis.com</domain>
        <domain includeSubdomains="true">firebase.googleapis.com</domain>
        <domain includeSubdomains="true">firestore.googleapis.com</domain>
        <domain includeSubdomains="true">identitytoolkit.googleapis.com</domain>
        <domain includeSubdomains="true">securetoken.googleapis.com</domain>
        
        <!-- Google Play Integrity API (for App Check) -->
        <domain includeSubdomains="true">playintegrity.googleapis.com</domain>
        <domain includeSubdomains="true">androidcheck-pa.googleapis.com</domain>
        
        <!-- Google APIs and CDN -->
        <domain includeSubdomains="true">googleapis.com</domain>
        <domain includeSubdomains="true">google.com</domain>
        <domain includeSubdomains="true">gstatic.com</domain>
        <domain includeSubdomains="true">googleusercontent.com</domain>
        
        <!-- Debug-friendly trust anchors -->
        <trust-anchors>
            <!-- Include system certificates -->
            <certificates src="system"/>
            <!-- Include user-added certificates for development -->
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>
    
    <!-- Development and debugging configuration -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">***********/16</domain>
        <domain includeSubdomains="true">**********/12</domain>
        
        <!-- Firebase Emulator Suite -->
        <domain includeSubdomains="true">firebase-emulator-suite</domain>
        <domain includeSubdomains="true">firebase-emulator</domain>
        
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>
    
    <!-- Base configuration for all other domains (Debug Mode) -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Include system certificates -->
            <certificates src="system"/>
            <!-- Include user-added certificates for development/testing -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
    
    <!-- Debug overrides for App Check token debugging -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>
